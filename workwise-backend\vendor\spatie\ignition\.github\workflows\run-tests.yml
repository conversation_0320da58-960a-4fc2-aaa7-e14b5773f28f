name: Run tests

on: [ push, pull_request ]

jobs:
    php-tests:
        runs-on: ${{ matrix.os }}
        strategy:
            fail-fast: false
            matrix:
                os: [ ubuntu-latest, windows-latest ]
                php: [ 8.0, 8.1, 8.2, 8.3 ]
                stability: [ prefer-lowest, prefer-stable ]

        name: P${{ matrix.php }} - ${{ matrix.stability }} - ${{ matrix.os }}

        steps:
            -   name: Checkout code
                uses: actions/checkout@v4

            -   name: Setup PHP
                uses: shivammathur/setup-php@v2
                with:
                    php-version: ${{ matrix.php }}
                    extensions: mbstring
                    coverage: none

            -   name: Setup problem matchers
                run: |
                    echo "::add-matcher::${{ runner.tool_cache }}/php.json"
                    echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

            -   name: Install dependencies
                run: composer update --${{ matrix.stability }} --prefer-dist --no-interaction

            -   name: Install extra package
                if: matrix.php != '8.0'
                run: composer require openai-php/client

            -   name: Execute tests
                run: vendor/bin/pest
