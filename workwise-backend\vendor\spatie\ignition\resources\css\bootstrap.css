@tailwind base;

html {
    font-size: clamp(13px, 1.3vw, 16px);
    @apply overflow-x-hidden;
    @apply overflow-y-scroll;
    font-feature-settings: 'calt' 0; /* disable contextual alternatives for '::$' misalignment */
    -webkit-marquee-increment: 1vw; /* See https://www.sarasoueidan.com/blog/safari-fluid-typography-bug-fix/ */
}

/* Exclude iframes like 1Password save modals */
*:not(iframe),
*:after,
*:before {
    position: relative;
}

*:focus {
    outline: 0 !important;
}

body {
    @apply font-sans;
    @apply leading-normal;
    @apply w-full;
    @apply text-gray-800;
    @apply dark:text-gray-200;
    @apply bg-gray-200;
    @apply dark:bg-gray-900;
}

/*
    For the `Auto` theme with a dark OS, `dark` class will be added to `<HTML class="auto">` by JS.
    Following CSS already sets the right background and text-color in this case.
*/

@media (prefers-color-scheme: dark) {
    html.auto body{
        @apply text-gray-200;
        @apply bg-gray-900;
    }
}

.scroll-target:target {
    content: "";
    display: block;
    position: absolute;
    top: -6rem;
}
