<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Event\InvalidCoverageMetadata;

use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass('PHPUnit\TestFixture\Event\InvalidCoverageMetadata\This\Does\Not\Exist')]
final class InvalidCoverageMetadataTest extends TestCase
{
    public function testOne(): void
    {
        $this->assertTrue(true);
    }
}
