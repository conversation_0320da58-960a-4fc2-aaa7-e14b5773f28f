{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "jsx": "react", "lib": ["dom", "es2020"], "noEmit": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "noUnusedLocals": true, "noUnusedParameters": true, "preserveConstEnums": true, "removeComments": false, "skipLibCheck": true, "sourceMap": false, "strict": true, "target": "esnext", "baseUrl": ".", "paths": {"*": ["*", "resources/js/*"]}}, "include": ["resources/**/*", "node_modules/microbundle/index.d.ts"], "exclude": ["**/compiled/**"]}