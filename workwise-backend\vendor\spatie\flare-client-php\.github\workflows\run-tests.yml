name: Run tests

on: [push, pull_request]

jobs:
    php-tests:
        runs-on: ${{ matrix.os }}

        strategy:
            matrix:
                php: [8.4, 8.3, 8.2, 8.1, 8.0]
                dependency-version: [prefer-stable]
                os: [ubuntu-latest]

        name: P${{ matrix.php }} - ${{ matrix.dependency-version }} - ${{ matrix.os }}

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ matrix.php }}
                  extensions: fileinfo, dom, curl, libxml, mbstring, bcmath, soap
                  coverage: none

            - name: Install PHP 8 dependencies
              run: composer update --${{ matrix.dependency-version }} --no-interaction --no-progress

            - name: Execute tests
              run: vendor/bin/pest
