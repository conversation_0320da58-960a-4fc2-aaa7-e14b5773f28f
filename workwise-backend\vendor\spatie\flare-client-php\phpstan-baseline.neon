parameters:
	ignoreErrors:
		-
			message: "#^Method Spatie\\\\FlareClient\\\\Frame\\:\\:toArray\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Frame.php

		-
			message: "#^Method Spatie\\\\FlareClient\\\\Glows\\\\Glow\\:\\:toArray\\(\\) return type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Glows/Glow.php

		-
			message: "#^Parameter \\$solution of method Spatie\\\\FlareClient\\\\Report\\:\\:addSolution\\(\\) has invalid type Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Report.php

		-
			message: "#^Parameter \\$solution of method Spatie\\\\FlareClient\\\\Report\\:\\:addSolution\\(\\) has invalid type Spatie\\\\Ignition\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Report.php

		-
			message: "#^Property Spatie\\\\FlareClient\\\\Report\\:\\:\\$glows type has no value type specified in iterable type array\\.$#"
			count: 1
			path: src/Report.php

		-
			message: "#^Access to property \\$aiGenerated on an unknown class Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Access to property \\$aiGenerated on an unknown class Spatie\\\\Ignition\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Call to method getDocumentationLinks\\(\\) on an unknown class Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Call to method getDocumentationLinks\\(\\) on an unknown class Spatie\\\\Ignition\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Call to method getSolutionDescription\\(\\) on an unknown class Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Call to method getSolutionDescription\\(\\) on an unknown class Spatie\\\\Ignition\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Call to method getSolutionTitle\\(\\) on an unknown class Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Call to method getSolutionTitle\\(\\) on an unknown class Spatie\\\\Ignition\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Class Spatie\\\\ErrorSolutions\\\\Contracts\\\\RunnableSolution not found\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Class Spatie\\\\Ignition\\\\Contracts\\\\RunnableSolution not found\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Parameter \\$solution of method Spatie\\\\FlareClient\\\\Solutions\\\\ReportSolution\\:\\:__construct\\(\\) has invalid type Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Parameter \\$solution of method Spatie\\\\FlareClient\\\\Solutions\\\\ReportSolution\\:\\:__construct\\(\\) has invalid type Spatie\\\\Ignition\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Parameter \\$solution of method Spatie\\\\FlareClient\\\\Solutions\\\\ReportSolution\\:\\:fromSolution\\(\\) has invalid type Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Parameter \\$solution of method Spatie\\\\FlareClient\\\\Solutions\\\\ReportSolution\\:\\:fromSolution\\(\\) has invalid type Spatie\\\\Ignition\\\\Contracts\\\\Solution\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Property Spatie\\\\FlareClient\\\\Solutions\\\\ReportSolution\\:\\:\\$solution has unknown class Spatie\\\\ErrorSolutions\\\\Contracts\\\\Solution as its type\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php

		-
			message: "#^Property Spatie\\\\FlareClient\\\\Solutions\\\\ReportSolution\\:\\:\\$solution has unknown class Spatie\\\\Ignition\\\\Contracts\\\\Solution as its type\\.$#"
			count: 1
			path: src/Solutions/ReportSolution.php
