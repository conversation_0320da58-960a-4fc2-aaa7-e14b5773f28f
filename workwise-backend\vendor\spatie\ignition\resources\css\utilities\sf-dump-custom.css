/*
    Dumps are hidden asap in errorPage.blade
    What follows is !important
*/

main pre.sf-dump {
    @apply block !important;
    @apply z-0 !important;
    @apply p-0 !important;
    @apply text-sm !important;
}

.sf-dump-public.sf-dump-highlight,
.sf-dump-protected.sf-dump-highlight,
.sf-dump-private.sf-dump-highlight,
.sf-dump-str.sf-dump-highlight,
.sf-dump-key.sf-dump-highlight {
    @apply bg-violet-500/10 !important;
}
.sf-dump-public.sf-dump-highlight-active,
.sf-dump-protected.sf-dump-highlight-active,
.sf-dump-private.sf-dump-highlight-active,
.sf-dump-str.sf-dump-highlight-active,
.sf-dump-key.sf-dump-highlight-active {
    @apply bg-amber-500/10 !important;
}


pre.sf-dump .sf-dump-search-wrapper {
    @apply items-center;
}

pre.sf-dump .sf-dump-search-wrapper > * {
    @apply border-0 !important;
}

pre.sf-dump .sf-dump-search-wrapper > input.sf-dump-search-input {
    @apply text-xs !important;
    @apply ~bg-white !important;
    @apply ~text-gray-800 !important;
    @apply h-8 !important;
    @apply px-2 !important;
}

pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-next, 
pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-previous {
    @apply bg-transparent !important;
    @apply ~text-gray-500 !important;
    @apply hover:text-indigo-500 !important;
    @apply px-1;
}

pre.sf-dump .sf-dump-search-wrapper svg path {
    @apply fill-current;
}

pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-count {
    @apply text-xs !important;
    @apply leading-normal !important;
    @apply px-4 !important;
    @apply ~text-gray-500 !important;
    @apply bg-transparent !important;
}

pre.sf-dump,
pre.sf-dump .sf-dump-default {
    @apply font-mono !important;
    @apply bg-transparent !important;
    @apply ~text-gray-800 !important;
}

pre.sf-dump .sf-dump-num {
    @apply ~text-emerald-600 !important;
}

pre.sf-dump .sf-dump-const {
    @apply font-normal !important;
    @apply text-violet-500 !important;
}

pre.sf-dump .sf-dump-str {
    @apply font-normal !important;
    @apply ~text-blue-600 !important;
}

pre.sf-dump .sf-dump-note {
    @apply ~text-indigo-600 !important;
}

pre.sf-dump .sf-dump-ref {
    @apply ~text-gray-500 !important;
}

pre.sf-dump .sf-dump-public,
pre.sf-dump .sf-dump-protected,
pre.sf-dump .sf-dump-private {
    @apply ~text-red-600 !important;
}

pre.sf-dump .sf-dump-meta {
    @apply ~text-indigo-600 !important;
}

pre.sf-dump .sf-dump-key {
    @apply ~text-violet-600 !important;
}

pre.sf-dump .sf-dump-index {
    @apply ~text-emerald-600 !important;
}

pre.sf-dump .sf-dump-ellipsis {
    @apply ~text-violet-600 !important;
}

pre.sf-dump .sf-dump-toggle {
    @apply ~text-gray-500 !important;
    @apply hover:text-indigo-500 !important;
}

pre.sf-dump .sf-dump-toggle span {
    @apply inline-flex !important;
    @apply items-center !important;
    @apply justify-center !important;
    @apply w-4 !important;
    @apply h-4 !important;
    font-size: 9px;
    @apply ~bg-gray-500/5 !important;
    @apply hover:bg-white !important;
    @apply dark:hover:bg-gray-900 !important;
    @apply rounded-full;
    @apply shadow-sm;
    @apply hover:shadow;
    @apply hover:text-indigo-500 !important;
    top: -2px;

}

pre.sf-dump .sf-dump-toggle:hover span {
    @apply bg-white !important;
    @apply dark:bg-gray-900 !important;
}
