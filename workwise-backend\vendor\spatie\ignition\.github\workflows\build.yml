name: Build

on:
    push:
        branches: [ main ]
    pull_request:
        branches: [ main ]

jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v4
              with:
                  ref: ${{ github.head_ref }}

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '18.x'

            - name: Restoring Yarn cache
              uses: actions/cache@v4
              with:
                  path: '**/node_modules'
                  key: ${{ runner.os }}-modules-${{ hashFiles('**/yarn.lock') }}

            - name: Installing NPM dependencies
              if: steps.yarn-cache.outputs.cache-hit != 'true'
              run: yarn install

            - name: Create bundle
              run: yarn run bundle-temp

            - name: Commit changes
              uses: stefanzweifel/git-auto-commit-action@v5
              with:
                  commit_message: Add new JS bundle build
