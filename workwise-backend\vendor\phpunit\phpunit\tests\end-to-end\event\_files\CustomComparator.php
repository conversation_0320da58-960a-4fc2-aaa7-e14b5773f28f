<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Event;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Comparator\Comparator;

final class CustomComparator extends Comparator
{
    public function accepts($expected, $actual): bool
    {
        return true;
    }

    public function assertEquals($expected, $actual, $delta = 0.0, $canonicalize = false, $ignoreCase = false): void
    {
    }
}
