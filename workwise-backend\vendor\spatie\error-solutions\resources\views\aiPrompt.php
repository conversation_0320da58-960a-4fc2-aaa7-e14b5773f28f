<?php /** @var \Spatie\Ignition\ignition\Solutions\OpenAi\OpenAiPromptViewModel $viewModel */ ?>

You are a very skilled PHP programmer.

<?php if($viewModel->applicationType()) { ?>
    You are working on a <?php echo $viewModel->applicationType() ?> application.
<?php } ?>

Use the following context to find a possible fix for the exception message at the end. Limit your answer to 4 or 5 sentences. Also include a few links to documentation that might help.

Use this format in your answer, make sure links are json:

FIX
insert the possible fix here
ENDFIX
LINKS
{"title": "Title link 1", "url": "URL link 1"}
{"title": "Title link 2", "url": "URL link 2"}
ENDLINKS
---

Here comes the context and the exception message:

Line: <?php echo $viewModel->line() ?>

File:
<?php echo $viewModel->file() ?>

Snippet including line numbers:
<?php echo $viewModel->snippet() ?>

Exception class:
<?php echo $viewModel->exceptionClass() ?>

Exception message:
<?php echo $viewModel->exceptionMessage() ?>
