PHPUnit %s by <PERSON> and contributors.

Runtime:       %s

Time: %s, Memory: %s

[4mBasic ANSI color highlighting support[0m
 [32m✔[0m Colorize with [36mno[2m·[22mcolor[0m [32m %f [2mms[0m
 [32m✔[0m Colorize with [36mone[2m·[22mcolor[0m [32m %f [2mms[0m
 [32m✔[0m Colorize with [36mmultiple[2m·[22mcolors[0m [32m %f [2mms[0m
 [32m✔[0m Colorize with [36minvalid[2m·[22mcolor[0m [32m %f [2mms[0m
 [32m✔[0m Colorize with [36mvalid[2m·[22mand[2m·[22minvalid[2m·[22mcolors[0m [32m %f [2mms[0m
 [32m✔[0m Colorize path [36m%ephp%eunit%etest.phpt[0m after [36mNULL[0m [32m %f [2mms[0m
 [32m✔[0m Colorize path [36m%ephp%eunit%etest.phpt[0m after [36;2;4mempt<PERSON>[0m [32m %f [2mms[0m
 [32m✔[0m Colorize path [36m%ephp%eunit%etest.phpt[0m after [36m%e[0m [32m %f [2mms[0m
 [32m✔[0m Colorize path [36m%ephp%eunit%etest.phpt[0m after [36m%ephp%e[0m [32m %f [2mms[0m
 [32m✔[0m Colorize path [36m%e_d-i.r%et-e_s.t.phpt[0m after [36;2;4mempty[0m [32m %f [2mms[0m
 [32m✔[0m dim($m) and colorize('dim',$m) return different ANSI codes [32m %f [2mms[0m
 [32m✔[0m Visualize all whitespace characters in [36mno-spaces[0m [32m %f [2mms[0m
 [32m✔[0m Visualize all whitespace characters in [36;2m·[22mspace[2m···[22minvaders[2m·[0m [32m %f [2mms[0m
 [32m✔[0m Visualize all whitespace characters in [36;2m⇥[22mindent,[2m·[22mspace[2m·[22mand[2m·[22m\n[2m↵[22m\r[2m⟵[0m [32m %f [2mms[0m
 [32m✔[0m Visualize whitespace but ignore EOL [32m %f [2mms[0m
 [32m✔[0m Prettify unnamed dataprovider[2m with data set [22m[36m0[0m [32m %f [2mms[0m
 [32m✔[0m Prettify unnamed dataprovider[2m with data set [22m[36m1[0m [32m %f [2mms[0m
 [32m✔[0m Prettify named dataprovider[2m with [22m[36mone[0m [32m %f [2mms[0m
 [32m✔[0m Prettify named dataprovider[2m with [22m[36mtwo[0m [32m %f [2mms[0m
 [32m✔[0m TestDox shows name of data set [36mone[0m with value [36m1[0m [32m %f [2mms[0m
 [32m✔[0m TestDox shows name of data set [36mtwo[0m with value [36m2[0m [32m %f [2mms[0m

[30;42mOK (21 tests, 21 assertions)[0m
