<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Event;

use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use RuntimeException;

final class ExceptionInDataProviderTest extends TestCase
{
    public static function provider(): array
    {
        throw new RuntimeException('message');
    }

    #[DataProvider('provider')]
    public function testOne(): void
    {
        $this->assertTrue(true);
    }
}
