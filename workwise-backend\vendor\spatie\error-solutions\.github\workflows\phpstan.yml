name: PHPStan

on:
  push:
    paths:
      - '**.php'
      - 'phpstan.neon.dist'
  pull_request:
    paths:
      - '**.php'
      - 'phpstan.neon.dist'


jobs:
  phpstan:
    name: phpstan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          coverage: none

      - name: Install composer dependencies
        uses: ramsey/composer-install@v3

      - name: Install extra package
        run: composer require openai-php/client

      - name: Run PHPStan
        run: ./vendor/bin/phpstan --error-format=github
