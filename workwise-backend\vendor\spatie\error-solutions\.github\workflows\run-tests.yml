name: Run tests

on: [push, pull_request]

jobs:
  php-tests:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        php: [8.1, 8.2, 8.3]
        laravel: [10.*, 11.*, 12.*]
        livewire: [2.11, 3.5.20]
        stability: [prefer-lowest, prefer-stable]
        os: [ubuntu-latest, windows-latest]
        exclude:
          - php: 8.1
            laravel: 11.*
          - php: 8.1
            laravel: 12.*
          - laravel: 11.*
            livewire: 2.11
          - laravel: 12.*
            livewire: 2.11

    name: P${{ matrix.php }} - L${{ matrix.laravel }} - LW${{ matrix.livewire }} -${{ matrix.stability }} - ${{ matrix.os }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: mbstring, fileinfo, pdo_sqlite
          coverage: none
          tools: composer:v2

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ matrix.os }}-php-${{ matrix.php }}-laravel-${{ matrix.laravel }}-composer-${{ matrix.stability }}-${{ hashFiles('**/composer.json') }}
          restore-keys: ${{ matrix.os }}-php-${{ matrix.php }}-laravel-${{ matrix.laravel }}-composer-${{ matrix.stability }}-

      -  name: Install dependencies
         run: |
           composer require "laravel/framework:${{ matrix.laravel }}" "nesbot/carbon:^2.72|^3.0" --no-interaction --no-update
           composer update --with="livewire/livewire:^${{ matrix.livewire }}" --${{ matrix.stability }} --prefer-dist --no-interaction --no-suggest

      -   name: Install extra package
          run: composer require openai-php/client

      - name: Execute tests
        run: vendor/bin/pest
