notifier: 'Flare Client'
language: PHP
framework_version: null
language_version: 7.3.2
exceptionClass: Exception
seenAt: 1546305825
message: 'this is an exception'
glows: {  }
solutions: {  }
stacktrace:
    -
        lineNumber: 46
        method: it_can_create_a_report_with_meta_data
        class: <PERSON>tie\FlareClient\Tests\ReportTest
        codeSnippet:
            25: '    {'
            26: '        $report = Report::createForThrowable(new Exception(''this is an exception''), new ConsoleContext());'
            27: ''
            28: '        $this->assertMatchesCodeSnippetSnapshot($report->toArray());'
            29: '    }'
            30: ''
            31: '    /** @test */'
            32: '    public function it_can_create_a_report_with_glows()'
            33: '    {'
            34: '        /** @var Report $report */'
            35: '        $report = Report::createForThrowable(new Exception(''this is an exception''), new ConsoleContext());'
            36: ''
            37: '        $report->addGlow(new Glow(''Glow 1'', ''info'', [''meta'' => ''data'']));'
            38: ''
            39: '        $this->assertMatchesCodeSnippetSnapshot($report->toArray());'
            40: '    }'
            41: ''
            42: '    /** @test */'
            43: '    public function it_can_create_a_report_with_meta_data()'
            44: '    {'
            45: '        /** @var Report $report */'
            46: '        $report = Report::createForThrowable(new Exception(''this is an exception''), new ConsoleContext());'
            47: ''
            48: '        $report->userProvidedContext([''meta'' => ['
            49: '            ''some'' => ''data'','
            50: '            ''something'' => ''more'''
            51: '        ]]);'
            52: ''
            53: '        $this->assertMatchesCodeSnippetSnapshot($report->toArray());'
            54: '    }'
            55: '}'
        file: /tests/ReportTest.php
    -
        lineNumber: 1154
        method: runTest
        class: PHPUnit\Framework\TestCase
        codeSnippet:
            1139: '     * @throws Throwable'
            1140: '     */'
            1141: '    protected function runTest()'
            1142: '    {'
            1143: '        if ($this->name === null) {'
            1144: '            throw new Exception('
            1145: '                ''PHPUnit\Framework\TestCase::$name must not be null.'''
            1146: '            );'
            1147: '        }'
            1148: ''
            1149: '        $testArguments = \array_merge($this->data, $this->dependencyInput);'
            1150: ''
            1151: '        $this->registerMockObjectsFromTestArguments($testArguments);'
            1152: ''
            1153: '        try {'
            1154: '            $testResult = $this->{$this->name}(...\array_values($testArguments));'
            1155: '        } catch (Throwable $exception) {'
            1156: '            if (!$this->checkExceptionExpectations($exception)) {'
            1157: '                throw $exception;'
            1158: '            }'
            1159: ''
            1160: '            if ($this->expectedException !== null) {'
            1161: '                $this->assertThat('
            1162: '                    $exception,'
            1163: '                    new ExceptionConstraint('
            1164: '                        $this->expectedException'
            1165: '                    )'
            1166: '                );'
            1167: '            }'
            1168: ''
            1169: '            if ($this->expectedExceptionMessage !== null) {'
        file: /vendor/phpunit/phpunit/src/Framework/TestCase.php
    -
        lineNumber: 842
        method: runBare
        class: PHPUnit\Framework\TestCase
        codeSnippet:
            827: ''
            828: '            if ($this->inIsolation) {'
            829: '                foreach ($hookMethods[''beforeClass''] as $method) {'
            830: '                    $this->$method();'
            831: '                }'
            832: '            }'
            833: ''
            834: '            $this->setExpectedExceptionFromAnnotation();'
            835: '            $this->setDoesNotPerformAssertionsFromAnnotation();'
            836: ''
            837: '            foreach ($hookMethods[''before''] as $method) {'
            838: '                $this->$method();'
            839: '            }'
            840: ''
            841: '            $this->assertPreConditions();'
            842: '            $this->testResult = $this->runTest();'
            843: '            $this->verifyMockObjects();'
            844: '            $this->assertPostConditions();'
            845: ''
            846: '            if (!empty($this->warnings)) {'
            847: '                throw new Warning('
            848: '                    \implode('
            849: '                        "\n",'
            850: '                        \array_unique($this->warnings)'
            851: '                    )'
            852: '                );'
            853: '            }'
            854: ''
            855: '            $this->status = BaseTestRunner::STATUS_PASSED;'
            856: '        } catch (IncompleteTest $e) {'
            857: '            $this->status        = BaseTestRunner::STATUS_INCOMPLETE;'
        file: /vendor/phpunit/phpunit/src/Framework/TestCase.php
    -
        lineNumber: 693
        method: run
        class: PHPUnit\Framework\TestResult
        codeSnippet:
            678: ''
            679: '                    case \PHPUnit\Util\Test::LARGE:'
            680: '                        $_timeout = $this->timeoutForLargeTests;'
            681: ''
            682: '                        break;'
            683: ''
            684: '                    case \PHPUnit\Util\Test::UNKNOWN:'
            685: '                        $_timeout = $this->defaultTimeLimit;'
            686: ''
            687: '                        break;'
            688: '                }'
            689: ''
            690: '                $invoker = new Invoker;'
            691: '                $invoker->invoke([$test, ''runBare''], [], $_timeout);'
            692: '            } else {'
            693: '                $test->runBare();'
            694: '            }'
            695: '        } catch (TimeoutException $e) {'
            696: '            $this->addFailure('
            697: '                $test,'
            698: '                new RiskyTestError('
            699: '                    $e->getMessage()'
            700: '                ),'
            701: '                $_timeout'
            702: '            );'
            703: ''
            704: '            $risky = true;'
            705: '        } catch (MockObjectException $e) {'
            706: '            $e = new Warning('
            707: '                $e->getMessage()'
            708: '            );'
        file: /vendor/phpunit/phpunit/src/Framework/TestResult.php
    -
        lineNumber: 796
        method: run
        class: PHPUnit\Framework\TestCase
        codeSnippet:
            781: '                ''configurationFilePath''                      => $configurationFilePath,'
            782: '                ''name''                                       => $this->getName(false),'
            783: '            ];'
            784: ''
            785: '            if (!$runEntireClass) {'
            786: '                $var[''methodName''] = $this->name;'
            787: '            }'
            788: ''
            789: '            $template->setVar('
            790: '                $var'
            791: '            );'
            792: ''
            793: '            $php = AbstractPhpProcess::factory();'
            794: '            $php->runTestJob($template->render(), $this, $result);'
            795: '        } else {'
            796: '            $result->run($this);'
            797: '        }'
            798: ''
            799: '        if (isset($oldErrorHandlerSetting)) {'
            800: '            $result->convertErrorsToExceptions($oldErrorHandlerSetting);'
            801: '        }'
            802: ''
            803: '        $this->result = null;'
            804: ''
            805: '        return $result;'
            806: '    }'
            807: ''
            808: '    /**'
            809: '     * @throws \Throwable'
            810: '     */'
            811: '    public function runBare(): void'
        file: /vendor/phpunit/phpunit/src/Framework/TestCase.php
    -
        lineNumber: 746
        method: run
        class: PHPUnit\Framework\TestSuite
        codeSnippet:
            731: '            return $result;'
            732: '        }'
            733: ''
            734: '        foreach ($this as $test) {'
            735: '            if ($result->shouldStop()) {'
            736: '                break;'
            737: '            }'
            738: ''
            739: '            if ($test instanceof TestCase || $test instanceof self) {'
            740: '                $test->setBeStrictAboutChangesToGlobalState($this->beStrictAboutChangesToGlobalState);'
            741: '                $test->setBackupGlobals($this->backupGlobals);'
            742: '                $test->setBackupStaticAttributes($this->backupStaticAttributes);'
            743: '                $test->setRunTestInSeparateProcess($this->runTestInSeparateProcess);'
            744: '            }'
            745: ''
            746: '            $test->run($result);'
            747: '        }'
            748: ''
            749: '        try {'
            750: '            foreach ($hookMethods[''afterClass''] as $afterClassMethod) {'
            751: '                if ($this->testCase === true && \class_exists($this->name, false) && \method_exists('
            752: '                    $this->name,'
            753: '                    $afterClassMethod'
            754: '                )) {'
            755: '                    \call_user_func([$this->name, $afterClassMethod]);'
            756: '                }'
            757: '            }'
            758: '        } catch (Throwable $t) {'
            759: '            $message = "Exception in {$this->name}::$afterClassMethod" . \PHP_EOL . $t->getMessage();'
            760: '            $error   = new SyntheticError($message, 0, $t->getFile(), $t->getLine(), $t->getTrace());'
            761: ''
        file: /vendor/phpunit/phpunit/src/Framework/TestSuite.php
    -
        lineNumber: 746
        method: run
        class: PHPUnit\Framework\TestSuite
        codeSnippet:
            731: '            return $result;'
            732: '        }'
            733: ''
            734: '        foreach ($this as $test) {'
            735: '            if ($result->shouldStop()) {'
            736: '                break;'
            737: '            }'
            738: ''
            739: '            if ($test instanceof TestCase || $test instanceof self) {'
            740: '                $test->setBeStrictAboutChangesToGlobalState($this->beStrictAboutChangesToGlobalState);'
            741: '                $test->setBackupGlobals($this->backupGlobals);'
            742: '                $test->setBackupStaticAttributes($this->backupStaticAttributes);'
            743: '                $test->setRunTestInSeparateProcess($this->runTestInSeparateProcess);'
            744: '            }'
            745: ''
            746: '            $test->run($result);'
            747: '        }'
            748: ''
            749: '        try {'
            750: '            foreach ($hookMethods[''afterClass''] as $afterClassMethod) {'
            751: '                if ($this->testCase === true && \class_exists($this->name, false) && \method_exists('
            752: '                    $this->name,'
            753: '                    $afterClassMethod'
            754: '                )) {'
            755: '                    \call_user_func([$this->name, $afterClassMethod]);'
            756: '                }'
            757: '            }'
            758: '        } catch (Throwable $t) {'
            759: '            $message = "Exception in {$this->name}::$afterClassMethod" . \PHP_EOL . $t->getMessage();'
            760: '            $error   = new SyntheticError($message, 0, $t->getFile(), $t->getLine(), $t->getTrace());'
            761: ''
        file: /vendor/phpunit/phpunit/src/Framework/TestSuite.php
    -
        lineNumber: 652
        method: doRun
        class: PHPUnit\TextUI\TestRunner
        codeSnippet:
            637: '        $result->setTimeoutForSmallTests($arguments[''timeoutForSmallTests'']);'
            638: '        $result->setTimeoutForMediumTests($arguments[''timeoutForMediumTests'']);'
            639: '        $result->setTimeoutForLargeTests($arguments[''timeoutForLargeTests'']);'
            640: ''
            641: '        if ($suite instanceof TestSuite) {'
            642: '            $this->processSuiteFilters($suite, $arguments);'
            643: '            $suite->setRunTestInSeparateProcess($arguments[''processIsolation'']);'
            644: '        }'
            645: ''
            646: '        foreach ($this->extensions as $extension) {'
            647: '            if ($extension instanceof BeforeFirstTestHook) {'
            648: '                $extension->executeBeforeFirstTest();'
            649: '            }'
            650: '        }'
            651: ''
            652: '        $suite->run($result);'
            653: ''
            654: '        foreach ($this->extensions as $extension) {'
            655: '            if ($extension instanceof AfterLastTestHook) {'
            656: '                $extension->executeAfterLastTest();'
            657: '            }'
            658: '        }'
            659: ''
            660: '        $result->flushListeners();'
            661: ''
            662: '        if ($this->printer instanceof ResultPrinter) {'
            663: '            $this->printer->printResult($result);'
            664: '        }'
            665: ''
            666: '        if (isset($codeCoverage)) {'
            667: '            if (isset($arguments[''coverageClover''])) {'
        file: /vendor/phpunit/phpunit/src/TextUI/TestRunner.php
    -
        lineNumber: 206
        method: run
        class: PHPUnit\TextUI\Command
        codeSnippet:
            191: '        if ($this->arguments[''listSuites'']) {'
            192: '            return $this->handleListSuites($exit);'
            193: '        }'
            194: ''
            195: '        if ($this->arguments[''listTests'']) {'
            196: '            return $this->handleListTests($suite, $exit);'
            197: '        }'
            198: ''
            199: '        if ($this->arguments[''listTestsXml'']) {'
            200: '            return $this->handleListTestsXml($suite, $this->arguments[''listTestsXml''], $exit);'
            201: '        }'
            202: ''
            203: '        unset($this->arguments[''test''], $this->arguments[''testFile'']);'
            204: ''
            205: '        try {'
            206: '            $result = $runner->doRun($suite, $this->arguments, $exit);'
            207: '        } catch (Exception $e) {'
            208: '            print $e->getMessage() . \PHP_EOL;'
            209: '        }'
            210: ''
            211: '        $return = TestRunner::FAILURE_EXIT;'
            212: ''
            213: '        if (isset($result) && $result->wasSuccessful()) {'
            214: '            $return = TestRunner::SUCCESS_EXIT;'
            215: '        } elseif (!isset($result) || $result->errorCount() > 0) {'
            216: '            $return = TestRunner::EXCEPTION_EXIT;'
            217: '        }'
            218: ''
            219: '        if ($exit) {'
            220: '            exit($return);'
            221: '        }'
        file: /vendor/phpunit/phpunit/src/TextUI/Command.php
    -
        lineNumber: 162
        method: main
        class: PHPUnit\TextUI\Command
        codeSnippet:
            147: ''
            148: '    /**'
            149: '     * @var bool'
            150: '     */'
            151: '    private $versionStringPrinted = false;'
            152: ''
            153: '    /**'
            154: '     * @throws \RuntimeException'
            155: '     * @throws \PHPUnit\Framework\Exception'
            156: '     * @throws \InvalidArgumentException'
            157: '     */'
            158: '    public static function main(bool $exit = true): int'
            159: '    {'
            160: '        $command = new static;'
            161: ''
            162: '        return $command->run($_SERVER[''argv''], $exit);'
            163: '    }'
            164: ''
            165: '    /**'
            166: '     * @throws \RuntimeException'
            167: '     * @throws \ReflectionException'
            168: '     * @throws \InvalidArgumentException'
            169: '     * @throws Exception'
            170: '     */'
            171: '    public function run(array $argv, bool $exit = true): int'
            172: '    {'
            173: '        $this->handleArguments($argv);'
            174: ''
            175: '        $runner = $this->createRunner();'
            176: ''
            177: '        if ($this->arguments[''test''] instanceof Test) {'
        file: /vendor/phpunit/phpunit/src/TextUI/Command.php
    -
        lineNumber: 61
        method: '[top]'
        class: null
        codeSnippet:
            32: '        define(''PHPUNIT_COMPOSER_INSTALL'', $file);'
            33: ''
            34: '        break;'
            35: '    }'
            36: '}'
            37: ''
            38: unset($file);
            39: ''
            40: 'if (!defined(''PHPUNIT_COMPOSER_INSTALL'')) {'
            41: '    fwrite('
            42: '        STDERR,'
            43: '        ''You need to set up the project dependencies using Composer:'' . PHP_EOL . PHP_EOL .'
            44: '        ''    composer install'' . PHP_EOL . PHP_EOL .'
            45: '        ''You can learn all about Composer on https://getcomposer.org/.'' . PHP_EOL'
            46: '    );'
            47: ''
            48: '    die(1);'
            49: '}'
            50: ''
            51: '$options = getopt('''', array(''prepend:''));'
            52: ''
            53: 'if (isset($options[''prepend''])) {'
            54: '    require $options[''prepend''];'
            55: '}'
            56: ''
            57: unset($options);
            58: ''
            59: 'require PHPUNIT_COMPOSER_INSTALL;'
            60: ''
            61: 'PHPUnit\TextUI\Command::main();'
            62: ''
        file: /vendor/phpunit/phpunit/phpunit
context:
    arguments: {  }
    meta:
        some: data
        something: more
stage: null
messageLevel: null
applicationPath: null
