name: Check & fix styling

on: [push]

jobs:
    style:
        runs-on: ubuntu-latest

        steps:
            -   name: Checkout code
                uses: actions/checkout@v4

            -   name: Fix style
                uses: docker://oskarstark/php-cs-fixer-ga
                with:
                    args: --config=.php_cs.php --allow-risky=yes

            -   name: Extract branch name
                shell: bash
                run: echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"
                id: extract_branch

            -   name: Commit changes
                uses: stefanzweifel/git-auto-commit-action@v5
                with:
                    commit_message: Fix styling
                    branch: ${{ steps.extract_branch.outputs.branch }}
                env:
                    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
