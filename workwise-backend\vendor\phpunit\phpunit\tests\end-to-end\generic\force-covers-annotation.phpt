--TEST--
phpunit ../../_files/BankAccountTest.php
--FILE--
<?php declare(strict_types=1);
$_SERVER['argv'][] = '--do-not-cache-result';
$_SERVER['argv'][] = '--configuration';
$_SERVER['argv'][] = __DIR__ . '/../_files/force-covers-annotation/phpunit.xml';
$_SERVER['argv'][] = __DIR__ . '/../_files/force-covers-annotation/tests/Test.php';

require_once __DIR__ . '/../../bootstrap.php';
(new PHPUnit\TextUI\Application)->run($_SERVER['argv']);
--EXPECTF--
PHPUnit %s by <PERSON> and contributors.

Runtime: %s
Configuration: %s

R                                                                   1 / 1 (100%)

Time: %s, Memory: %s

There was 1 risky test:

1) PHPUnit\TestFixture\Test::testOne
This test does not define a code coverage target but is expected to do so

%s:%d

OK, but there were issues!
Tests: 1, Assertions: 1, Risky: 1.
